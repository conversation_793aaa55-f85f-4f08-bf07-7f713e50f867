import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:downloadsfolder/downloadsfolder.dart';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../../../core/di/service_locator.dart';

/// Provider for export data state
final exportDataProvider =
    StateNotifierProvider<ExportDataNotifier, ExportDataState>((ref) {
  return ExportDataNotifier();
});

/// Export data state
class ExportDataState {
  final bool isExporting;
  final bool isCompleted;
  final bool isSuccessful;
  final String? errorMessage;
  final String? filePath;

  const ExportDataState({
    this.isExporting = false,
    this.isCompleted = false,
    this.isSuccessful = false,
    this.errorMessage,
    this.filePath,
  });

  ExportDataState copyWith({
    bool? isExporting,
    bool? isCompleted,
    bool? isSuccessful,
    String? errorMessage,
    String? filePath,
  }) {
    return ExportDataState(
      isExporting: isExporting ?? this.isExporting,
      isCompleted: isCompleted ?? this.isCompleted,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
      filePath: filePath ?? this.filePath,
    );
  }
}

/// Export data notifier
class ExportDataNotifier extends StateNotifier<ExportDataState> {
  ExportDataNotifier() : super(const ExportDataState());

  static const String _backupFilename = 'lekky_export_101.csv';
  static const int _backupFormatVersion = 101;

  /// Export data to Downloads folder using MediaStore API
  Future<void> exportData() async {
    state = state.copyWith(isExporting: true, isCompleted: false);

    try {
      // Get database helper
      final databaseHelper = serviceLocator<DatabaseHelper>();
      final db = await databaseHelper.database;

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      // Convert to MeterEntry objects
      final entries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        entries.add(MeterEntry(
          id: reading['id'] as int?,
          date: DateTime.parse(reading['date'] as String),
          reading: reading['value'] as double,
          amountToppedUp: 0,
          typeCode: 0,
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp['id'] as int?,
          date: DateTime.parse(topUp['date'] as String),
          reading: 0,
          amountToppedUp: topUp['amount'] as double,
          typeCode: 1,
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$_backupFormatVersion'],
        ['Date', 'Type', 'Amount'],
        ...entries.map((entry) => [
              entry.timestamp.toIso8601String(),
              entry.typeCode.toString(),
              entry.amountToppedUp > 0
                  ? entry.amountToppedUp.toString()
                  : entry.reading.toString(),
            ]),
      ];

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);
      final csvBytes = Uint8List.fromList(csv.codeUnits);

      // Save to Downloads folder using MediaStore API
      await DownloadsFolder.saveFile(
        fileName: _backupFilename,
        fileData: csvBytes,
      );

      Logger.info('Data exported successfully to Downloads folder');

      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: true,
        filePath: _backupFilename,
      );
    } catch (e) {
      Logger.error('Failed to export data: $e');

      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Reset the export state
  void resetState() {
    state = const ExportDataState();
  }
}
