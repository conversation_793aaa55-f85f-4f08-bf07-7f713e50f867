// File: test/core/utils/event_bus_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/event_bus.dart';

void main() {
  group('EventBus', () {
    late EventBus eventBus;

    setUp(() {
      eventBus = EventBus();
    });

    test('should fire and receive events', () async {
      // Arrange
      EventType? receivedEvent;
      final subscription = eventBus.stream.listen((event) {
        receivedEvent = event;
      });

      // Act
      eventBus.fire(EventType.dataUpdated);
      await Future.delayed(Duration.zero); // Allow event to propagate

      // Assert
      expect(receivedEvent, equals(EventType.dataUpdated));

      // Cleanup
      subscription.cancel();
    });

    test('should handle multiple listeners', () async {
      // Arrange
      final receivedEvents = <EventType>[];
      final subscription1 = eventBus.stream.listen((event) {
        receivedEvents.add(event);
      });
      final subscription2 = eventBus.stream.listen((event) {
        receivedEvents.add(event);
      });

      // Act
      eventBus.fire(EventType.dataUpdated);
      await Future.delayed(Duration.zero); // Allow events to propagate

      // Assert
      expect(receivedEvents.length, equals(2));
      expect(receivedEvents, everyElement(equals(EventType.dataUpdated)));

      // Cleanup
      subscription1.cancel();
      subscription2.cancel();
    });

    test('should handle multiple event types', () async {
      // Arrange
      final receivedEvents = <EventType>[];
      final subscription = eventBus.stream.listen((event) {
        receivedEvents.add(event);
      });

      // Act
      eventBus.fire(EventType.dataUpdated);
      eventBus.fire(EventType.averagesCalculating);
      eventBus.fire(EventType.settingsUpdated);
      await Future.delayed(Duration.zero); // Allow events to propagate

      // Assert
      expect(receivedEvents.length, equals(3));
      expect(receivedEvents[0], equals(EventType.dataUpdated));
      expect(receivedEvents[1], equals(EventType.averagesCalculating));
      expect(receivedEvents[2], equals(EventType.settingsUpdated));

      // Cleanup
      subscription.cancel();
    });

    test('should not receive events after subscription is cancelled', () async {
      // Arrange
      final receivedEvents = <EventType>[];
      final subscription = eventBus.stream.listen((event) {
        receivedEvents.add(event);
      });

      // Act
      eventBus.fire(EventType.dataUpdated);
      await Future.delayed(Duration.zero);
      subscription.cancel();
      eventBus.fire(EventType.settingsUpdated);
      await Future.delayed(Duration.zero);

      // Assert
      expect(receivedEvents.length, equals(1));
      expect(receivedEvents[0], equals(EventType.dataUpdated));
    });
  });
}
