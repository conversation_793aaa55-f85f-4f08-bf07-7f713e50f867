import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/shared/widgets/integer_input_field.dart';

void main() {
  group('IntegerInputField', () {
    testWidgets('should display correct suffix text', (WidgetTester tester) async {
      int? capturedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: IntegerInputField(
              value: 5,
              onChanged: (value) => capturedValue = value,
              suffixText: 'days',
              labelText: 'Days in Advance',
              minValue: 0,
              maxValue: 99,
            ),
          ),
        ),
      );

      // Verify suffix text is displayed
      expect(find.text('days'), findsOneWidget);
      expect(find.text('Days in Advance'), findsOneWidget);
    });

    testWidgets('should accept valid values within range', (WidgetTester tester) async {
      int? capturedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: IntegerInputField(
              value: null,
              onChanged: (value) => capturedValue = value,
              suffixText: 'days',
              labelText: 'Days in Advance',
              minValue: 0,
              maxValue: 99,
            ),
          ),
        ),
      );

      // Test boundary values
      await tester.enterText(find.byType(TextField), '0');
      expect(capturedValue, 0);

      await tester.enterText(find.byType(TextField), '99');
      expect(capturedValue, 99);

      await tester.enterText(find.byType(TextField), '50');
      expect(capturedValue, 50);
    });

    testWidgets('should show correct helper text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: IntegerInputField(
              value: 5,
              onChanged: (value) {},
              suffixText: 'days',
              labelText: 'Days in Advance',
              minValue: 0,
              maxValue: 99,
            ),
          ),
        ),
      );

      expect(find.text('Must be between 0 and 99 days'), findsOneWidget);
    });

    testWidgets('should auto-select text when focused', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: IntegerInputField(
              value: 25,
              onChanged: (value) {},
              suffixText: 'days',
              labelText: 'Days in Advance',
              minValue: 0,
              maxValue: 99,
            ),
          ),
        ),
      );

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '25');

      // Tap to focus
      await tester.tap(find.byType(TextField));
      await tester.pump();

      // Text should be selected (this is handled by the focus listener)
      expect(textField.focusNode?.hasFocus, true);
    });
  });
}
