// File: test/core/utils/performance_monitor_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/utils/performance_monitor.dart';

void main() {
  group('PerformanceMonitor', () {
    setUp(() {
      // Clear performance data before each test
      PerformanceMonitor.clear();
    });

    test('should track operation timing', () async {
      // Arrange
      const operationName = 'test_operation';
      
      // Act
      PerformanceMonitor.startOperation(operationName);
      await Future.delayed(Duration(milliseconds: 10));
      final duration = PerformanceMonitor.endOperation(operationName);
      
      // Assert
      expect(duration.inMilliseconds, greaterThanOrEqualTo(10));
    });

    test('should handle missing start time gracefully', () {
      // Arrange
      const operationName = 'missing_operation';
      
      // Act
      final duration = PerformanceMonitor.endOperation(operationName);
      
      // Assert
      expect(duration, equals(Duration.zero));
    });

    test('should calculate average duration', () async {
      // Arrange
      const operationName = 'avg_test';
      
      // Act - perform multiple operations
      for (int i = 0; i < 3; i++) {
        PerformanceMonitor.startOperation(operationName);
        await Future.delayed(Duration(milliseconds: 10));
        PerformanceMonitor.endOperation(operationName);
      }
      
      final avgDuration = PerformanceMonitor.getAverageDuration(operationName);
      
      // Assert
      expect(avgDuration.inMilliseconds, greaterThanOrEqualTo(10));
    });

    test('should return zero average for unknown operation', () {
      // Arrange
      const operationName = 'unknown_operation';
      
      // Act
      final avgDuration = PerformanceMonitor.getAverageDuration(operationName);
      
      // Assert
      expect(avgDuration, equals(Duration.zero));
    });

    test('should provide operation summary', () async {
      // Arrange
      const operationName = 'summary_test';
      
      // Act
      PerformanceMonitor.startOperation(operationName);
      await Future.delayed(Duration(milliseconds: 5));
      PerformanceMonitor.endOperation(operationName);
      
      final summary = PerformanceMonitor.getOperationSummary(operationName);
      
      // Assert
      expect(summary['operationName'], equals(operationName));
      expect(summary['count'], equals(1));
      expect(summary['averageDuration'], greaterThanOrEqualTo(5));
      expect(summary['lastDuration'], greaterThanOrEqualTo(5));
    });

    test('should provide empty summary for unknown operation', () {
      // Arrange
      const operationName = 'unknown_summary';
      
      // Act
      final summary = PerformanceMonitor.getOperationSummary(operationName);
      
      // Assert
      expect(summary['operationName'], equals(operationName));
      expect(summary['count'], equals(0));
      expect(summary['averageDuration'], equals(0));
      expect(summary['lastDuration'], equals(0));
    });

    test('should time async operations', () async {
      // Arrange
      const operationName = 'async_test';
      
      // Act
      final result = await PerformanceMonitor.timeOperation(
        operationName,
        () async {
          await Future.delayed(Duration(milliseconds: 10));
          return 'test_result';
        },
      );
      
      final summary = PerformanceMonitor.getOperationSummary(operationName);
      
      // Assert
      expect(result, equals('test_result'));
      expect(summary['count'], equals(1));
      expect(summary['lastDuration'], greaterThanOrEqualTo(10));
    });

    test('should handle async operation errors', () async {
      // Arrange
      const operationName = 'error_test';
      
      // Act & Assert
      await expectLater(
        PerformanceMonitor.timeOperation(
          operationName,
          () async {
            await Future.delayed(Duration(milliseconds: 5));
            throw Exception('Test error');
          },
        ),
        throwsException,
      );
      
      // Should still record the timing
      final summary = PerformanceMonitor.getOperationSummary(operationName);
      expect(summary['count'], equals(1));
    });

    test('should limit operation history', () async {
      // Arrange
      const operationName = 'history_test';
      
      // Act - perform more than 10 operations
      for (int i = 0; i < 15; i++) {
        PerformanceMonitor.startOperation(operationName);
        await Future.delayed(Duration(milliseconds: 1));
        PerformanceMonitor.endOperation(operationName);
      }
      
      final summary = PerformanceMonitor.getOperationSummary(operationName);
      
      // Assert - should only keep last 10
      expect(summary['count'], equals(10));
    });

    test('should clear all data', () async {
      // Arrange
      PerformanceMonitor.startOperation('test1');
      PerformanceMonitor.endOperation('test1');
      PerformanceMonitor.startOperation('test2');
      PerformanceMonitor.endOperation('test2');
      
      // Act
      PerformanceMonitor.clear();
      
      // Assert
      final summaries = PerformanceMonitor.getAllSummaries();
      expect(summaries, isEmpty);
    });
  });
}
